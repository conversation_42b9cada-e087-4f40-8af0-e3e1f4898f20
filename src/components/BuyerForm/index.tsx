"use client";

import { useEffect, useMemo, useState } from "react";
import { FormProvider } from "react-hook-form";

import { LoadingOverlay } from "@/components";
import { useBumpSelection } from "@/hooks/useBumpSelection";
import { useBuyerFormSchema } from "@/hooks/useBuyerFormSchema";
import { useCreateOrderSubmitHandler } from "@/hooks/useCreateOrderSubmitHandler";
import { useDocumentAndPhone } from "@/hooks/useDocumentAndPhone";
import { useFormDataManager } from "@/hooks/useFormDataManager";
import { usePaymentState } from "@/hooks/usePaymentState";
import { usePixelTracking } from "@/hooks/usePixelTracking";
import { useTotalPriceCalculator } from "@/hooks/useTotalPriceCalculator";
import { useTrialPeriod } from "@/hooks/useTrialPeriod";
import { useProductStore } from "@/store/productStore";
import { InstallmentsResponse } from "@/types/instalmentsList";
import { PaymentOption } from "@/types/paymentOptions";
import { Product, ProductChargeType } from "@/types/product";

import { AlertSellerRegistrationDialog } from "../ui/AlertSellerRegistrationDialog";
import { BumpSelection } from "./BumpSelection";
import { BuyerFormFields } from "./BuyerFormFields";
import { PaymentButton } from "./PaymentButton";
import { PaymentContent } from "./PaymentContent";
import { PaymentMethodSelection } from "./PaymentMethodSelection";
import { PurchaseSummary } from "./PurchaseSummary";

interface BuyerFormProps {
  product: Product;
  productInstallments: InstallmentsResponse;
}

const BuyerForm = ({ product, productInstallments }: BuyerFormProps) => {
  const { setIsSubscription } = useProductStore();

  console.log("product", product);

  useEffect(() => {
    const isMainProductSubscription =
      product?.charge_type === ProductChargeType.SUBSCRIPTION;
    const hasSubscriptionBump = product.bumps?.some(
      bump => bump.charge_type === ProductChargeType.SUBSCRIPTION
    );

    setIsSubscription(isMainProductSubscription || hasSubscriptionBump);
  }, [product.charge_type, product.bumps, setIsSubscription]);

  const {
    selectedOption,
    setSelectedOption,
    selectedInstalmentDescription,
    setSelectedInstalmentDescription,
    getCardStyles,
  } = usePaymentState();

  const { haveBrazilianDocument, setHaveBrazilianDocument } =
    useDocumentAndPhone();

  const { formMethods, translations } = useBuyerFormSchema(
    haveBrazilianDocument,
    selectedOption
  );

  const {
    handleSubmit,
    register,
    formState: { errors: formErrors },
    control: formControl,
    setValue: updateFormValue,
    resetField: clearFormField,
  } = formMethods;

  const [selectedInstallment, setSelectedInstallment] = useState<string>("");

  useEffect(() => {
    if (
      selectedOption === PaymentOption.CreditCard &&
      productInstallments?.installments &&
      productInstallments.installments.length > 0 &&
      !selectedInstallment
    ) {
      const lastInstalment =
        productInstallments.installments[
          productInstallments.installments.length - 1
        ];
      setSelectedInstallment(lastInstalment.installments.toString());
      setSelectedInstalmentDescription(lastInstalment.description);
    }
  }, [
    selectedOption,
    productInstallments?.installments,
    selectedInstallment,
    setSelectedInstalmentDescription,
  ]);

  const {
    selectedBumps,
    selectedProductBumpsId,
    bumpInstallments,
    handleBumpSelection,
  } = useBumpSelection({
    product,
    productInstallments,
    selectedInstallment,
    onAddToCart: () => handleAddToCartEvent(),
  });

  const { handlePhoneChange, phoneValue } = useFormDataManager(updateFormValue);

  const { hasTrialDays, firstChargeDate } = useTrialPeriod(product);

  const totalPrice = useTotalPriceCalculator({
    selectedOption,
    selectedInstallment,
    productInstallments,
    product,
    selectedBumps,
  });

  const {
    metaPixelEvents,
    tiktokPixelEvents,
    handleFacebookPixelEvent,
    handleTiktokPixelEvent,
    handleGoogleAdsEvent,
    handleGoogleAdsBeginCheckout,
    handlePurchaseEvent,
    handlePurchaseEventTiktok,
    handleAddToCartEvent,
  } = usePixelTracking({
    product,
    totalPrice: Number(totalPrice),
    selectedOption,
    selectedBumps,
  });

  const { onSubmit, isLoading, showModal, setShowModal } =
    useCreateOrderSubmitHandler(
      haveBrazilianDocument,
      selectedOption,
      product,
      selectedProductBumpsId,
      selectedInstalmentDescription
    );

  const handleSwitchDocumentType = (isInternational: boolean) => {
    setHaveBrazilianDocument(!isInternational);
    if (isInternational) clearFormField("document");
  };

  const paymentHandlers = useMemo(
    () => ({
      [PaymentOption.CreditCard]: () => {
        handleFacebookPixelEvent("InitiateCheckout");
        handleTiktokPixelEvent("InitiateCheckout");
        handleGoogleAdsEvent();
      },
      [PaymentOption.Pix]: () => {
        handleFacebookPixelEvent("InitiateCheckout");
        handleTiktokPixelEvent("InitiateCheckout");
        handlePurchaseEvent(metaPixelEvents);
        handlePurchaseEventTiktok(tiktokPixelEvents);
        handleGoogleAdsEvent();
      },
      [PaymentOption.Boleto]: () => {
        handleFacebookPixelEvent("InitiateCheckout");
        handleTiktokPixelEvent("InitiateCheckout");
        handlePurchaseEvent(metaPixelEvents);
        handlePurchaseEventTiktok(tiktokPixelEvents);
        handleGoogleAdsBeginCheckout();
      },
    }),
    [
      handleFacebookPixelEvent,
      handleTiktokPixelEvent,
      handleGoogleAdsEvent,
      handlePurchaseEvent,
      handlePurchaseEventTiktok,
      handleGoogleAdsBeginCheckout,
      metaPixelEvents,
      tiktokPixelEvents,
    ]
  );

  const handlePaymentClick = (option: PaymentOption) => {
    if (typeof window !== "undefined") {
      const handler = paymentHandlers[option];
      if (handler) {
        handler();
      }
    }
  };

  useEffect(() => {
    if (
      selectedOption === PaymentOption.Pix ||
      selectedOption === PaymentOption.Boleto
    ) {
      setHaveBrazilianDocument(true);
      setSelectedInstallment("");
      setSelectedInstalmentDescription("");
    }
  }, [
    selectedOption,
    setHaveBrazilianDocument,
    setSelectedInstalmentDescription,
  ]);

  return (
    <FormProvider {...formMethods}>
      {isLoading && <LoadingOverlay />}
      <form onSubmit={handleSubmit(onSubmit as any)}>
        <BuyerFormFields
          errors={formErrors}
          register={register}
          control={formControl}
          translations={translations}
          haveBrazilianDocument={haveBrazilianDocument}
          phoneValue={phoneValue}
          onPhoneChange={handlePhoneChange}
          onSwitchDocumentType={handleSwitchDocumentType}
          selectedOption={selectedOption}
        />

        <div>
          <PaymentMethodSelection
            product={product}
            selectedOption={selectedOption}
            setSelectedOption={setSelectedOption}
            getCardStyles={getCardStyles}
          />

          <PaymentContent
            selectedOption={selectedOption}
            productInstallments={productInstallments}
            setSelectedInstalmentDescription={setSelectedInstalmentDescription}
            setSelectedInstallment={setSelectedInstallment}
          />

          <BumpSelection
            product={product}
            selectedOption={selectedOption}
            selectedInstallment={selectedInstallment}
            bumpInstallments={bumpInstallments}
            onBumpSelect={handleBumpSelection}
            translations={translations}
          />

          <PurchaseSummary
            product={product}
            selectedOption={selectedOption}
            selectedInstallment={selectedInstallment}
            selectedBumps={selectedBumps}
            bumpInstallments={bumpInstallments}
            totalPrice={Number(totalPrice)}
            hasTrialDays={!!hasTrialDays}
            firstChargeDate={firstChargeDate}
            productInstallments={productInstallments}
            translations={translations}
          />

          <PaymentButton
            selectedOption={selectedOption}
            product={product}
            translations={translations}
            onClick={handlePaymentClick}
          />
        </div>
      </form>
      <AlertSellerRegistrationDialog
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        setShowModal={setShowModal}
      />
    </FormProvider>
  );
};

export default BuyerForm;
