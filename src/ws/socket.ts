import Echo from "@ably/laravel-echo";
import * as Ably from "ably";

declare global {
  interface Window {
    Echo: Echo;
    Ably: typeof Ably;
  }
}

export function createSocketConnection() {
  if (typeof window !== "undefined") {
    if (!window.Echo) {
      window.Ably = Ably;
      window.Echo = new Echo({
        broadcaster: "ably",
        key: process.env.NEXT_PUBLIC_ABLY_KEY,
        authEndpoint: `${process.env.NEXT_PUBLIC_BASE_URL}/broadcasting/auth`,
      });
    }
  }
}
