{"name": "tb-web-checkout", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext ts --ext tsx --ext js", "lint:fix": "eslint . --ext ts --ext tsx --ext js --fix", "format": "prettier --write .", "format:check": "prettier --check .", "check": "npm run lint && npm run format:check", "fix": "npm run lint:fix && npm run format", "setup:dev": "npm run prepare && npm run fix && echo 'Ambiente configurado com sucesso!'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky"}, "engines": {"node": "^20"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "dependencies": {"@ably/laravel-echo": "^1.0.6", "@carbon/icons-react": "^11.57.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.69.0", "@thebank/creditcard-js": "^1.0.0", "ably": "1.x", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.6.2", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.484.0", "next": "15.2.4", "next-intl": "^3.26.5", "node-fetch": "^3.3.2", "npm-check-updates": "^17.1.16", "react": "^19.0.0", "react-barcode": "^1.6.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-phone-number-input": "^3.4.12", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@sentry/nextjs": "^9.22.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/canvas-confetti": "^1.9.0", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/mocha": "^10.0.10", "@types/node": "^22", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-config-next": "14.2.26", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "eslint-plugin-simple-import-sort": "^12.1.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-websocket-mock": "^2.5.0", "lint-staged": "^15.5.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}